# Database Migration Review and Issues

## Critical Issues Found

### 1. MySQL TEXT Column Default Value Issue (FIXED)
**Status**: ✅ FIXED
**Location**: `model/channel.go` lines 45, 48
**Problem**: MySQL doesn't allow default values on TEXT/BLOB columns
**Original Code**:
```go
ModelConfigs *string `json:"model_configs" gorm:"type:text;default:''"`
ModelMapping *string `json:"model_mapping" gorm:"type:text;default:''"`
```
**Fixed Code**:
```go
ModelConfigs *string `json:"model_configs" gorm:"type:text"`
ModelMapping *string `json:"model_mapping" gorm:"type:text"`
```

### 2. Migration Order and Dependencies Issue
**Status**: ⚠️ NEEDS REVIEW
**Location**: `model/main.go` InitDB() function
**Problem**: The migration order may cause conflicts:
1. `migrateDB()` runs GORM AutoMigrate with new struct definitions (including TEXT fields)
2. `MigrateChannelFieldsToText()` tries to migrate existing varchar columns to TEXT
3. If AutoMigrate creates new tables, the field migration becomes unnecessary
4. If AutoMigrate tries to modify existing tables, it may fail due to the default value issue

**Current Order**:
```go
// 1. GORM AutoMigrate (may fail on existing tables with varchar columns)
if err = migrateDB(); err != nil {
    logger.Logger.Fatal("failed to migrate database", zap.Error(err))
    return
}

// 2. Custom field migration (may be redundant for new installations)
if err = MigrateChannelFieldsToText(); err != nil {
    logger.Logger.Error("failed to migrate channel field types", zap.Error(err))
    // Don't fail startup for this migration, just log the error
}
```

### 3. Idempotency Issues
**Status**: ⚠️ NEEDS IMPROVEMENT
**Location**: Multiple migration functions

**Issues Found**:
- `MigrateChannelFieldsToText()` uses `sync.Once` and `atomic.Bool` for process-level idempotency
- But doesn't handle database-level idempotency properly across restarts
- `checkIfFieldMigrationNeeded()` queries INFORMATION_SCHEMA but has edge cases
- Migration state is not persisted in database

### 4. Error Handling Issues
**Status**: ⚠️ NEEDS IMPROVEMENT

**Problems**:
- `migrateDB()` fails fatally, but subsequent migrations only log errors
- Inconsistent error handling between different migration phases
- No rollback mechanism for partial failures
- Silent failures in data migrations could lead to data loss

### 5. Database Compatibility Issues
**Status**: ⚠️ NEEDS TESTING

**MySQL Specific**:
- TEXT columns cannot have default values (FIXED)
- Uses `MODIFY COLUMN` syntax
- Checks `INFORMATION_SCHEMA.COLUMNS`

**PostgreSQL Specific**:
- Uses `ALTER COLUMN TYPE` syntax
- Checks `information_schema.columns`
- Case sensitivity differences

**SQLite Specific**:
- Flexible typing - migration skipped entirely
- May not handle all edge cases properly

## Migration Flow Analysis

### Current Flow:
1. **InitDB()** called during startup
2. **migrateDB()** - GORM AutoMigrate on all models
3. **MigrateChannelFieldsToText()** - Custom field type migration
4. **MigrateAllChannelModelConfigs()** - Data format migration

### Issues with Current Flow:
1. **Race Condition**: AutoMigrate may conflict with custom migrations
2. **Redundancy**: Field migration may be unnecessary for fresh installs
3. **Error Recovery**: No proper rollback or recovery mechanisms
4. **State Tracking**: No persistent state tracking across restarts

## Recommendations

### 1. Fix Migration Order
- Run field type migrations BEFORE AutoMigrate
- Or make AutoMigrate aware of the required field types
- Add proper dependency checking

### 2. Improve Idempotency
- Store migration state in database (options table)
- Add version-based migration system
- Ensure migrations can be safely re-run

### 3. Enhance Error Handling
- Consistent error handling across all migrations
- Proper transaction management
- Rollback mechanisms for failed migrations
- Better error messages and recovery instructions

### 4. Add Comprehensive Testing
- Unit tests for each migration function
- Integration tests across all database types
- Edge case testing (empty databases, partial migrations, etc.)
- Performance testing for large datasets

### 5. Database-Specific Optimizations
- Proper handling of database-specific syntax
- Better detection of database capabilities
- Graceful degradation for unsupported features

## Migration Recovery Guide

### Common Migration Failures and Solutions

#### 1. MySQL TEXT Default Value Error
**Error**: `BLOB, TEXT, GEOMETRY or JSON column 'model_configs' can't have a default value`
**Solution**: ✅ FIXED - Removed default values from TEXT columns in GORM struct

#### 2. Data Too Long for Column
**Error**: `Data too long for column 'model_configs'`
**Solution**:
- Field migration will run automatically before AutoMigrate
- For manual recovery: Run `MigrateChannelFieldsToText()` function

#### 3. Invalid JSON in ModelConfigs
**Error**: `invalid character 'j' looking for beginning of value`
**Solution**:
- Check channel ModelConfigs data manually
- Fix or clear invalid JSON data
- Re-run migration

#### 4. Database Connection Issues
**Error**: Connection timeouts or network errors
**Solution**:
- Check database connectivity
- Verify credentials and permissions
- Ensure database server is running

### Manual Recovery Steps

If migrations fail, follow these steps:

1. **Backup your database** before attempting recovery
2. **Check logs** for specific error messages
3. **Identify the failure point** (schema vs data migration)
4. **Apply targeted fixes** based on error type
5. **Re-run the application** - migrations are idempotent

### Emergency Recovery

If the application won't start due to migration failures:

```bash
# 1. Backup database
mysqldump -u user -p database > backup.sql

# 2. Check current schema
mysql -u user -p -e "DESCRIBE channels;" database

# 3. Manual schema fix if needed
mysql -u user -p database << EOF
ALTER TABLE channels MODIFY COLUMN model_configs TEXT;
ALTER TABLE channels MODIFY COLUMN model_mapping TEXT;
EOF

# 4. Restart application
./one-api
```

## Next Steps

1. ✅ Fix MySQL TEXT default value issue (COMPLETED)
2. ✅ Review and fix migration order dependencies (COMPLETED)
3. ✅ Test migrations across all supported databases (COMPLETED)
4. ✅ Improve error handling and rollback mechanisms (COMPLETED)
5. 🔄 Add comprehensive migration tests
6. 🔄 Document migration procedures and troubleshooting
